<?php
namespace app\admin\logic;

use app\common\basics\Logic;
use app\common\model\PurchaserPackage;
use app\common\model\PurchaserPackageOrder;
use think\facade\Db;

/**
 * 采购套餐逻辑
 * Class PurchaserPackageLogic
 * @package app\admin\logic
 */
class PurchaserPackageLogic extends Logic
{
    /**
     * 获取套餐列表
     * @param array $params
     * @return array
     */
    public static function lists($params)
    {
        $limit = $params['limit'] ?? 15;
        $page = $params['page'] ?? 1;

        $model = PurchaserPackage::withTrashed(); // 查询所有数据，包括软删除的

        if (isset($params['status']) && $params['status'] != '') {
            $model = $model->where('status', '=', $params['status']);
        }

        $count = $model->count();
        $lists = $model->page($page, $limit)->order('sort desc, id desc')->select()->toArray();

       

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * 添加套餐
     * @param array $params
     * @return bool
     */
    public static function add($params)
    {
        try {
            $time = time();
            $data = [
                'name' => $params['name'],
                'purchaser_count' => $params['purchaser_count'],
                'price' => $params['price'],
                'status' => $params['status'] ?? 1,
                'sort' => $params['sort'] ?? 100,
                'create_time' => $time,
                'update_time' => $time,
            ];
            PurchaserPackage::create($data);
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 编辑套餐
     * @param array $params
     * @return bool
     */
    public static function edit($params)
    {
        try {
            $model = PurchaserPackage::find($params['id']);
            if (!$model) {
                self::setError('套餐不存在');
                return false;
            }
            $time = time();
            $data = [
                'name' => $params['name'],
                'purchaser_count' => $params['purchaser_count'],
                'price' => $params['price'],
                'status' => $params['status'],
                'sort' => $params['sort'],
                'update_time' => $time,
            ];
            $model->save($data);
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 删除套餐
     * @param int $id
     * @return bool
     */
    public static function del($id)
    {
        return Db::name('purchaser_package')->where('id', $id)->delete();
    }

    /**
     * 获取套餐详情
     * @param int $id
     * @return array|null
     */
    public static function detail($id)
    {
        return PurchaserPackage::withTrashed()->find($id);
    }
    
    /**
     * 修改状态
     * @param array $params
     * @return bool
     */
    public static function status($params)
    {
        $model = PurchaserPackage::withTrashed()->find($params['id']);
        if (!$model) {
            self::setError('套餐不存在');
            return false;
        }
        $model->status = $params['status'];
        return $model->save();
    }

    /**
     * 获取购买记录列表
     * @param array $params
     * @return array
     */
    public static function records($params)
    {
        $limit = $params['limit'] ?? 15;
        $page = $params['page'] ?? 1;

        $model = new PurchaserPackageOrder();
        
        if (!empty($params['shop_id'])) {
            $model = $model->where('shop_id', '=', $params['shop_id']);
        }
        
        if (!empty($params['package_id'])) {
            $model = $model->where('package_id', '=', $params['package_id']);
        }

        if (isset($params['pay_status']) && $params['pay_status'] != '') {
            $model = $model->where('pay_status', '=', $params['pay_status']);
        }

        $count = $model->count();
        $lists = $model->with(['shop', 'user'])
                       ->page($page, $limit)
                       ->order('id', 'desc')
                       ->select()
                       ->toArray();

        return ['count' => $count, 'lists' => $lists];
    }
}
